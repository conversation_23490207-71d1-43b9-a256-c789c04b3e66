{"cells": [{"cell_type": "code", "execution_count": 6, "id": "c047685a", "metadata": {}, "outputs": [], "source": ["import requests\n", "\n", "url = \"https://api.perplexity.ai/chat/completions\"\n", "headers = {\"Authorization\": \"Bearer 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\"}"]}, {"cell_type": "code", "execution_count": 71, "id": "64ea19d7", "metadata": {}, "outputs": [], "source": ["payload = {\n", "  \"model\": \"sonar\",\n", "  \"messages\": [\n", "    {\n", "      \"role\": \"system\",\n", "      \"content\": \"\"\"You are an expert payment aggregator merchant onboarding specialist with exceptional reasoning skills and deep knowledge of \n", "      merchant category codes (MCCs). Your task is to accurately classify the business to a single correct MCC based on the website URL provided \n", "      by the user. You must use the official MCC classifications of Visa as your reference. \n", "      **NOTE: While the company may operate in multiple business segments or sell a variety of products at a corporate level, \n", "      for the purpose of this analysis, focus exclusively on the specified outlet/subsidiary/business unit and its primary operations, products, \n", "      services, and revenue sources. Base the Merchant Category Code assignment only on this outlet’s activities, not the broader parent company.**\"\"\"\n", "    },\n", "    {\n", "      \"role\": \"user\",\n", "      \"content\": \"\"\"\n", "      Conduct a comprehensive analysis of the website https://www.indiamart.com/sai-nurseries by visiting it and assign the most appropriate \n", "      Merchant Category Code (MCC) based on the latest data available on the site.\n", "\n", "      **CRITICAL**:\n", "      - Do not rely completly on external resources like Google search results or other websites such as review sites or wikipedia; instead, base your analysis solely on the content of the provided website.\n", "      - Ensure that all information is derived directly from the website itself. Avoid using external sources.\n", "      - The website may be unoperational or inaccessible due to various reasons. In such cases, provide a detailed explanation of why the site cannot be accessed.\n", "      - For each step, ensure that you have extracted and analyzed the necessary information before proceeding to the next step.\n", "      \n", "\n", "      **Step 1: Business Profile Analysis and Key Information Extraction**\n", "      - **Visit the website** and extract and analyze the following information from the provided website only.\n", "      - **Target these pages of the site**: home page, about us page, products page, catalog page, services page, contact us page.\n", "      - Understand the core business operations and primary revenue streams at a detailed level.\n", "      - Precisely analyze the core target customer segments and market reach.\n", "      - Precisely analyze the primary products sold or services offered. \n", "      \n", "      **Step 2: MCC Assignment Criteria**\n", "      - **Use only the provided MCC reference source and pick mcc from the Similar Merchants section of the PDF**: https://usa.visa.com/dam/VCOM/download/merchants/visa-merchant-data-standards-manual.pdf\n", "      - Assign a single, most appropriate 4-digit MCC that best represents the site's primary business activity.\n", "      - **MANDATORY EXCLUSION:** Reject any MCC codes ending in 99 or 999 (these are excluded codes).\n", "      - If multiple MCCs seem plausible, choose the one that aligns most closely with the site's primary focus.\n", "      \n", "      **Step 3: Assignment Validation**\n", "      - Verify that the chosen MCC accurately reflects the dominant business activity.\n", "      - Provide specific reasoning for the MCC selection over alternatives.\n", "      \n", "      **You are only allowed to answer in the specified format. If you are unable to find the required information, return 'NA' for the relevant fields.**\n", "      \"\"\"\n", "    }\n", "  ],\n", "  \"response_format\": {\n", "    \"type\": \"json_schema\",\n", "    \"json_schema\": {\n", "      \"name\": \"business_analysis\",\n", "      \"strict\": True,\n", "      \"schema\": {\n", "        \"type\": \"object\",\n", "        \"properties\": {\n", "          \"business_profile\": {\n", "            \"type\": \"object\",\n", "            \"properties\": {\n", "                \"DATE\": {\n", "                  \"type\": \"string\",\n", "                  \"format\": \"date\",\n", "                  \"description\": \"Date when the analysis was conducted\"\n", "                },\n", "                \"TIME\": {\n", "                  \"type\": \"string\",\n", "                  \"format\": \"time\",\n", "                  \"description\": \"Time when the analysis was conducted\"\n", "                },\n", "              \"primary_business_activity\": {\n", "                \"type\": \"string\",\n", "                \"description\": \"Detailed description of main operations\"\n", "              },\n", "              \"revenue_model\": {\n", "                \"type\": \"string\",\n", "                \"description\": \"How the business generates income (100 words or less)\"\n", "              },\n", "              \"target_market\": {\n", "                \"type\": \"string\",\n", "                \"description\": \"Primary customer segments (100 words or less)\"\n", "              },\n", "              \"industry_sector\": {\n", "                \"type\": \"string\",\n", "                \"description\": \"Specific industry classification (100 words or less)\"\n", "              }\n", "            },\n", "            \"required\": [\"primary_business_activity\", \"revenue_model\", \"target_market\", \"industry_sector\"],\n", "            \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "          },\n", "          \"mcc_assignment\": {\n", "            \"type\": \"object\",\n", "            \"properties\": {\n", "              \"assigned_mcc\": {\n", "                \"type\": \"string\",\n", "                \"description\": \"4-digit code (XXXX), avoid ending in 99 or 999\"\n", "              },\n", "              \"mcc_category_name\": {\n", "                \"type\": \"string\",\n", "                \"description\": \"Official MCC category title\"\n", "              },\n", "              \"mcc_description\": {\n", "                \"type\": \"string\",\n", "                \"description\": \"Standard definition of this MCC (100 words or less)\"\n", "              },\n", "              \"justification\": {\n", "                \"type\": \"string\",\n", "                \"description\": \"Why this MCC best fits the business (100 words or less)\"\n", "              }\n", "            },\n", "            \"required\": [\"assigned_mcc\", \"mcc_category_name\", \"mcc_description\", \"justification\"],\n", "            \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "          }\n", "        },\n", "        \"required\": [\"business_profile\", \"mcc_assignment\"],\n", "        \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "      }\n", "    }\n", "  },\n", "  \"web_search_options\": {\n", "    \"search_context_size\": \"low\"\n", "  },\n", "  \"temperature\": 0.0\n", "}"]}, {"cell_type": "code", "execution_count": 72, "id": "10917fce", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["('{\"business_profile\":{\"DATE\":\"2025-08-02\",\"TIME\":\"04:11:04Z\",\"primary_business_activity\":\"Sai '\n", " 'Nurseries operates as a plant nursery specializing in the cultivation, '\n", " 'wholesale, and retail of various plants including forestry plants, '\n", " 'ornamental plants, fruit plants, garden plants, and agricultural plants. The '\n", " 'business also offers landscaping design services and related nursery '\n", " 'products. Their operations focus on growing and supplying live plants and '\n", " 'seeds to customers primarily in the horticulture, agriculture, and '\n", " 'landscaping sectors.\",\"revenue_model\":\"The business generates revenue '\n", " 'primarily through the sale of live plants, seeds, and nursery products to '\n", " 'wholesale and retail customers. Additional income streams include providing '\n", " 'landscaping design and related services. The sales are conducted via the '\n", " 'IndiaMART platform, targeting both individual consumers and commercial '\n", " 'clients such as landscapers and agricultural '\n", " 'businesses.\",\"target_market\":\"The primary customers are wholesale and retail '\n", " 'buyers interested in forestry, ornamental, fruit, and garden plants, '\n", " 'including landscapers, agricultural producers, and gardening enthusiasts. '\n", " 'The market reach appears regional within India, focusing on customers '\n", " 'seeking live plants and landscaping '\n", " 'services.\",\"industry_sector\":\"Horticulture and nursery services sector, '\n", " 'including plant cultivation, wholesale and retail of live plants and seeds, '\n", " 'and landscaping design '\n", " 'services.\"},\"mcc_assignment\":{\"assigned_mcc\":\"0782\",\"mcc_category_name\":\"Lawn '\n", " 'and Garden Supply Stores\",\"mcc_description\":\"Establishments primarily '\n", " 'engaged in the retail sale of lawn and garden supplies, including live '\n", " 'plants, seeds, and related products.\",\"justification\":\"The primary business '\n", " 'activity of Sai Nurseries is the cultivation and sale of live plants and '\n", " 'nursery products, which aligns closely with the Lawn and Garden Supply '\n", " 'Stores category. Although landscaping services are offered, the dominant '\n", " 'revenue source is the sale of plants and seeds. Other MCCs related to '\n", " 'agriculture or landscaping services are less fitting because the business '\n", " 'focuses on plant retail rather than agricultural production or purely '\n", " 'service-based landscaping. MCC 0782 best captures the core retail nursery '\n", " 'operations without including excluded codes ending in 99 or 999.\"}}')\n"]}], "source": ["from pprint import pprint\n", "response = requests.post(url, headers=headers, json=payload).json()\n", "pprint(response[\"choices\"][0][\"message\"][\"content\"])"]}, {"cell_type": "code", "execution_count": null, "id": "cb059143", "metadata": {}, "outputs": [], "source": ["count 6"]}, {"cell_type": "code", "execution_count": 68, "id": "33d9ef1d", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'error': {'message': 'Invalid JSON schema. Format DD/MM/YYYY is not supported by Sonar API',\n", "  'type': 'invalid_parameter',\n", "  'code': 400}}"]}, "execution_count": 68, "metadata": {}, "output_type": "execute_result"}], "source": ["response"]}, {"cell_type": "code", "execution_count": 21, "id": "e24c1ef6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["('{\"business_profile\":{\"company_name\":\"Amazon.in\",\"primary_business_activity\":\"Amazon.in '\n", " 'operates as a comprehensive online marketplace, facilitating the sale of a '\n", " 'vast range of products including electronics, apparel, home goods, '\n", " 'groceries, and more, both directly and through third-party '\n", " 'sellers.\",\"revenue_model\":\"Amazon.in generates income primarily through '\n", " 'direct online retail sales, commissions and fees from third-party sellers on '\n", " 'its marketplace, advertising services, and subscription services such as '\n", " 'Amazon Prime. The majority of revenue is derived from online store sales and '\n", " 'marketplace fees.[1][2][3]\",\"target_market\":\"The primary customer segments '\n", " 'are individual consumers and households across India seeking a wide variety '\n", " 'of products online, as well as small and medium businesses using the '\n", " 'platform to reach customers.[1][2]\",\"industry_sector\":\"E-commerce and online '\n", " 'retail marketplace, with a focus on multi-category consumer goods and '\n", " 'services.[2][3]\"},\"mcc_assignment\":{\"assigned_mcc\":\"5311\",\"mcc_category_name\":\"Department '\n", " 'Stores\",\"mcc_description\":\"Merchants that sell a wide variety of goods, '\n", " 'including apparel, electronics, home goods, and groceries, typically under '\n", " 'one brand or platform, both directly and via third-party '\n", " 'sellers.\",\"justification\":\"MCC 5311 best fits Amazon.in as it is a '\n", " 'multi-category online marketplace and retailer, analogous to a department '\n", " 'store in the digital space, with the majority of its transaction volume and '\n", " 'revenue coming from the sale of diverse consumer goods.[2][3]\"}}')\n"]}], "source": ["from pprint import pprint\n", "pprint(response[\"choices\"][0][\"message\"][\"content\"])"]}, {"cell_type": "code", "execution_count": null, "id": "3efd2f23", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 5}