{"model": "sonar", "messages": [{"role": "system", "content": "You are an expert payment aggregator merchant onboarding specialist with exceptional reasoning skills and deep knowledge of merchant category codes (MCCs). Your task is to accurately classify the business to a single correct MCC based solely on the activities, products, and services of the specific outlet as presented on the provided website URL. **Do not consider the parent company’s broader operations or its registered business activities.** Use only the **Similar Merchants** descriptions from the Visa Merchant Data Standards Manual (April 2025) as your reference for MCC assignment."}, {"role": "user", "content": "Conduct a comprehensive analysis of the website https://www.spencers.in by visiting it and assign the most appropriate Merchant Category Code (MCC) based on the latest data available on the site, using only the **Similar Merchants** descriptions from the Visa Merchant Data Standards Manual (April 2025, https://usa.visa.com/dam/VCOM/download/merchants/visa-merchant-data-standards-manual.pdf).\n\n**Step 1: Business Profile Analysis and Key Information Extraction**\n- **Visit the website** and extract and analyze information from the following pages only: home page, about us page, products page, catalog page, services page, contact us page.\n- Understand the core business operations and primary revenue streams of the outlet as presented on the website.\n- Precisely analyze the core target customer segments and market reach as shown on the website.\n- Precisely analyze the primary products sold or services offered on the website.\n\n**Step 2: MCC Assignment Criteria**\n- **Use only the Similar Merchants descriptions** from the Visa Merchant Data Standards Manual (April 2025) to assign a single, most appropriate 4-digit MCC that best represents the outlet’s primary business activity as presented on the website.\n- **MANDATORY EXCLUSION:** Reject any MCC codes ending in 99 or 999 (these are 'not elsewhere classified' codes).\n- If multiple MCCs seem plausible based on the Similar Merchants descriptions, choose the one that aligns most closely with the outlet’s primary focus on the website.\n\n**Step 3: Assignment Validation**\n- Verify that the chosen MCC accurately reflects the outlet’s dominant business activity as shown on the website.\n- Provide specific reasoning for the MCC selection over alternatives, referencing only the website’s content and the Similar Merchants descriptions.\n\n**You are only allowed to answer in the specified format. If you are unable to find the required information, return 'NA' for the relevant fields.**"}], "response_format": {"type": "json_schema", "json_schema": {"name": "business_analysis", "strict": true, "schema": {"type": "object", "properties": {"business_profile": {"type": "object", "properties": {"primary_business_activity": {"type": "string", "description": "Detailed description of main operations"}, "revenue_model": {"type": "string", "description": "How the business generates income (100 words or less)"}, "target_market": {"type": "string", "description": "Primary customer segments (100 words or less)"}, "industry_sector": {"type": "string", "description": "Specific industry classification (100 words or less)"}}, "required": ["primary_business_activity", "revenue_model", "target_market", "industry_sector"], "additionalProperties": false}, "mcc_assignment": {"type": "object", "properties": {"assigned_mcc": {"type": "string", "description": "4-digit code (XXXX), avoid ending in 99 or 999"}, "mcc_category_name": {"type": "string", "description": "Official MCC category title"}, "mcc_description": {"type": "string", "description": "Standard definition of this MCC (100 words or less)"}, "justification": {"type": "string", "description": "Why this MCC best fits the business (100 words or less)"}}, "required": ["assigned_mcc", "mcc_category_name", "mcc_description", "justification"], "additionalProperties": false}}, "required": ["business_profile", "mcc_assignment"], "additionalProperties": false}}}, "web_search_options": {"search_context_size": "medium"}, "temperature": 0.0}